import 'dart:ui';
import 'dart:convert';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart'; // Para formatação de data/hora
import 'package:quycky/app/features/friendship/domain/entities/chat_message_entity.dart'; // Importando a entidade
import 'package:quycky/app/features/friendship/domain/entities/report_player_params_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/core/enumerators/egame_action.dart'; // Será usado para o 'action' base do socket
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/game_socket/game_socket_service.dart';
import 'package:quycky/app/theme/colors.dart';
// Novos imports para DTOs e Enumerator
import 'package:quycky/app/features/friendship/domain/dtos/chat_message_dto.dart';
import 'package:quycky/app/features/friendship/domain/dtos/send_message_dto.dart';
import 'package:quycky/app/features/friendship/domain/dtos/get_messages_between_users_dto.dart';
import 'package:quycky/app/features/friendship/domain/dtos/mark_as_read_dto.dart';
import 'package:quycky/app/features/friendship/domain/dtos/get_unread_count_dto.dart';
import 'package:quycky/app/features/friendship/domain/dtos/get_conversations_dto.dart';
import 'package:quycky/app/features/friendship/presenter/store/chat_store.dart';
import 'package:quycky/app/features/friendship/domain/enumerators/chat_action_enumerator.dart';
import 'package:quycky/app/features/friendship/domain/dtos/get_messages_dto.dart'; // Adicionado
import 'package:quycky/app/features/game/domain/dto/game_message_dto.dart'; // Import para GameMessageDTO
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/avatar.dart'; // Importando o Avatar
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/app/widgets/friend_options_bottom_sheet.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/widgets/report_player_dialog.dart'; // Re-adicionado
import 'package:quycky/app/widgets/block_player_dialog.dart'; // Re-adicionado
import 'package:quycky/app/widgets/social_login_buttons.dart';
import 'package:quycky/core/entities/abs_mappable.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:sizer/sizer.dart';
import 'package:uuid/uuid.dart'; // Para gerar IDs únicos

// A classe Teste não é mais necessária, será removida.

class FriendChat extends StatefulWidget {
  final UserEntity friend;
  const FriendChat({super.key, required this.friend});

  @override
  State<FriendChat> createState() => _FriendChatState();
}

class _FriendChatState extends State<FriendChat> {
  final _controller = Modular.get<FriendshipController>();
  final _userStore = Modular.get<UserStore>();
  final _chatStore = Modular.get<ChatStore>();
  bool _showInfoBanner = true;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final double _messageTextFieldBorderRadius = 122;
  final _uuid = Uuid();
  bool _isLoading = false;
  String? _connectionId; // Para armazenar o connectionId do socket
  final _socketService = Modular.get<GameSocketService>();

  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    setState(() {
      _isLoading = value;
    });
  }

  bool get showInfoBanner => _showInfoBanner && _messages.isEmpty;

  bool get _isUserSocialLogged =>
      _userStore.state.user.appleId?.isNotEmpty ==
          true || // Adicionado null check
      _userStore.state.user.googleId?.isNotEmpty ==
          true; // Adicionado null check

  set showInfoBanner(bool value) {
    setState(() {
      _showInfoBanner = false;
    });
  }

  // Lista de mensagens, agora será populada pelo socket
  final List<ChatMessageEntity> _messages = [];

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  @override
  void dispose() {
    _socketService.removeMessageHandler(
        EGameAction.chat, _handleSocketMessageWrapper);
    _socketService.removeConnectionHandler(
        _onSocketConnected); // Garante a remoção do handler de conexão
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeChat() async {
    if (_socketService.isConnected) {
      _connectionId = _socketService.connectionId;
      _listenToChatMessages();
      _fetchInitialMessages();
    } else {
      // Adiciona um handler para ser notificado quando a conexão for estabelecida
      _socketService.addConnectionHandler(_onSocketConnected);
      // Se não estiver conectado nem conectando, inicia a conexão
      if (!_socketService.isConnecting) {
        _socketService.connect();
      }
      // isLoading será true até _onSocketConnected ou _fetchInitialMessages ser chamado
      setState(() {
        _isLoading = true;
      });
    }
  }

  void _onSocketConnected() {
    // Este método é chamado pelo GameSocketService quando a conexão é estabelecida
    _connectionId = _socketService.connectionId;
    if (_connectionId != null && _connectionId!.isNotEmpty) {
      _listenToChatMessages();
      _fetchInitialMessages();
    } else {
      // Caso raro, mas connectionId pode não estar disponível imediatamente
      print("Socket conectado, mas connectionId ainda não disponível.");
      setState(() {
        _isLoading = false; // Para o loading se não puder prosseguir
      });
    }
    // É importante remover o handler aqui para evitar chamadas múltiplas
    // se o _initializeChat for chamado novamente por algum motivo enquanto o handler ainda está registrado.
    // No entanto, se o GameSocketService gerencia bem os handlers (não adiciona duplicatas), isso pode não ser estritamente necessário.
    // Mas por segurança, é bom remover.
    // _socketService.removeConnectionHandler(_onSocketConnected); // Removido daqui, pois pode ser necessário em reconexões
  }

  void _listenToChatMessages() {
    // Remove qualquer handler antigo para evitar duplicidade antes de adicionar um novo.
    _socketService.removeMessageHandler(
        EGameAction.chat, _handleSocketMessageWrapper);
    _socketService.addMessageHandler(
        EGameAction.chat, _handleSocketMessageWrapper);
  }

  void _handleSocketMessageWrapper(GameMessageDTO gameMessage) {
    // O 'data' do GameMessageDTO para EGameAction.chat deve ser o ChatMessageDTO (ou seu Map)
    dynamic chatPayload = gameMessage.data;

    if (chatPayload is String) {
      try {
        chatPayload = json.decode(chatPayload);
      } catch (e) {
        print(
            "Erro ao decodificar payload do chat (String): $e. Payload: $chatPayload");
        return;
      }
    }

    if (chatPayload is Map<String, dynamic>) {
      final action = chatPayload['action'] as String?;
      final specificData =
          chatPayload['data']; // Este é o 'data' do ChatMessageDTO

      if (action != null && specificData != null) {
        _handleChatMessage(action, specificData);
      } else {
        print(
            "Chat message wrapper: action ou specificData é null. Full gameMessage.data: $chatPayload");
      }
    } else {
      print(
          "Received chat message with unexpected data type: ${chatPayload.runtimeType}. Data: $chatPayload");
    }
  }

  void _handleChatMessage(String action, dynamic data) {
    // O 'data' aqui é o 'data' específico da ação do chat, não o 'data' do GameMessageDTO
    if (!mounted) return; // Verifica se o widget ainda está montado

    setState(() {
      _isLoading =
          false; // Garante que o loading pare ao receber qualquer mensagem relevante
      switch (action) {
        case ChatActionEnumerator.messageReceived:
          final newMessage =
              ChatMessageEntity.fromMap(data as Map<String, dynamic>);
          // Evita adicionar duplicatas se a mensagem já foi adicionada localmente ao enviar
          if (!_messages.any((m) =>
              m.id == newMessage.id ||
              (m.senderIdentifier == newMessage.senderIdentifier &&
                  m.content == newMessage.content &&
                  m.createdAt.difference(newMessage.createdAt).inSeconds.abs() <
                      5 &&
                  m.id == 0))) {
            _messages.add(newMessage);
            _messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            // Adiciona a mensagem ao ChatStore
            _chatStore.addMessage(newMessage);
          }
          _scrollToBottom();
          if (newMessage.receiverIdentifier == _userStore.state.user.id &&
              !newMessage.isRead) {
            _markMessagesAsRead([newMessage.id]);
            // Incrementa a contagem de mensagens não lidas no ChatStore
            _chatStore.incrementUnreadCount(newMessage.senderIdentifier);
          }
          break;
        case ChatActionEnumerator.messageList:
        case ChatActionEnumerator.messagesBetweenPlayers:
          final List<dynamic> messageListRaw =
              data['messages'] as List<dynamic>;
          _messages.clear();
          _messages.addAll(messageListRaw.map(
              (msg) => ChatMessageEntity.fromMap(msg as Map<String, dynamic>)));
          _messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));

          // Atualiza as mensagens atuais no ChatStore
          _chatStore.updateCurrentMessages(_messages);

          _scrollToBottom();

          final unreadMessageIds = _messages
              .where((m) =>
                  !m.isRead &&
                  m.receiverIdentifier == _userStore.state.user.id &&
                  m.id > 0)
              .map((m) => m.id)
              .toList();
          if (unreadMessageIds.isNotEmpty) {
            _markMessagesAsRead(unreadMessageIds);
          }

          // Zera a contagem de mensagens não lidas para este usuário
          _chatStore.clearUnreadCount(widget.friend.identifier ?? '');
          break;
        case ChatActionEnumerator.messageSent:
          final sentMessage =
              ChatMessageEntity.fromMap(data as Map<String, dynamic>);
          final existingMessageIndex = _messages.indexWhere((m) =>
                  m.senderIdentifier == sentMessage.senderIdentifier &&
                  m.receiverIdentifier == sentMessage.receiverIdentifier &&
                  m.content == sentMessage.content &&
                  (m.id == 0 ||
                      m.id == sentMessage.id ||
                      m.createdAt
                              .difference(sentMessage.createdAt)
                              .inSeconds
                              .abs() <
                          5) // ID temporário ou muito próximo
              );

          if (existingMessageIndex != -1) {
            // Se a mensagem do servidor tiver um ID e a local não (ou for temporária), atualiza.
            if (sentMessage.id > 0 &&
                (_messages[existingMessageIndex].id == 0 ||
                    _messages[existingMessageIndex].id != sentMessage.id)) {
              _messages[existingMessageIndex] = sentMessage;
            } else if (_messages[existingMessageIndex].id == 0 &&
                sentMessage.id == 0) {
              // Ambos são temporários, mas o do servidor pode ter um timestamp mais preciso.
              _messages[existingMessageIndex] = sentMessage;
            }
          } else {
            _messages.add(sentMessage);
            _messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          }
          _scrollToBottom();
          break;
        case ChatActionEnumerator.messagesUpdated:
        case ChatActionEnumerator.countAsRead:
          final List<dynamic>? updatedIdsDynamic =
              data['updatedMessageIds'] as List<dynamic>?;
          if (updatedIdsDynamic != null) {
            final updatedIds =
                updatedIdsDynamic.map((id) => id as int).toList();
            for (var id in updatedIds) {
              final index = _messages.indexWhere((msg) => msg.id == id);
              if (index != -1) {
                _messages[index] = _messages[index].copyWith(isRead: true);
              }
            }
          }
          _fetchUnreadCount(); // Atualiza a contagem geral
          break;
        case ChatActionEnumerator.unreadCountUpdated:
          final totalUnread = data['totalUnreadCount'] as int?;
          final userUnreadCounts =
              data['userUnreadCounts'] as Map<String, dynamic>?;

          if (totalUnread != null) {
            print("Total de mensagens não lidas atualizado: $totalUnread");
          }

          // Atualiza as contagens individuais por usuário no ChatStore
          if (userUnreadCounts != null) {
            userUnreadCounts.forEach((userIdentifier, count) {
              _chatStore.updateUnreadCount(userIdentifier, count as int);
            });
          }
          break;
        default:
          print("Ação de chat não tratada: $action");
      }
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Todos os métodos a seguir devem estar DENTRO da classe _FriendChatState

  void _sendMessage() {
    if (_messageController.text.isEmpty) {
      print(
          "Não é possível enviar mensagem: texto vazio ou connectionId inválido.");
      return;
    }

    // final user = _userStore.state.user;

    final sendMessageData = SendMessageDTO(
      receiverIdentifier: widget.friend.identifier ?? '',
      content: _messageController.text,
    );

    final chatMessageDto = ChatMessageDTO<SendMessageDTO>(
      action: ChatActionEnumerator.sendMessage,
      connectionId: '',
      data: sendMessageData,
    );

    _socketService.sendMessage(_socketService.createMessage(
      EGameAction.chat,
      data: chatMessageDto, // Passa o ChatMessageDTO que é AbsMappable
    ));

    // final tempMessage = ChatMessageEntity(
    //   id: 0, // ID Temporário
    //   senderIdentifier: user.id,
    //   receiverIdentifier: widget.friend.id,
    //   content: _messageController.text,
    //   createdAt: DateTime.now(),
    //   isRead: false,
    // );

    if (mounted) {
      setState(() {
        // _messages.add(tempMessage);
        _messageController.clear();
      });
    }
    _scrollToBottom(); // Esta chamada está correta aqui
    _handleChangeMessageTextFieldBorderRadius();
  }

  void _fetchInitialMessages() {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    final getMessagesData = GetMessagesBetweenUsersDTO(
      otherUserIdentifier: widget.friend.identifier ?? '',
    );

    final chatMessageDto = ChatMessageDTO<GetMessagesBetweenUsersDTO>(
      action: ChatActionEnumerator.getMessagesBetweenUsers,
      connectionId: _connectionId,
      data: getMessagesData,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  void _markMessagesAsRead(List<int> messageIds) {
    if (messageIds.isEmpty || _connectionId == null || _connectionId!.isEmpty)
      return;

    final validMessageIds = messageIds.where((id) => id > 0).toList();
    if (validMessageIds.isEmpty) return;

    final markAsReadData = MarkAsReadDTO(messageIds: validMessageIds);

    final chatMessageDto = ChatMessageDTO<MarkAsReadDTO>(
      action: ChatActionEnumerator.markAsRead,
      connectionId: _connectionId,
      data: markAsReadData,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  void _fetchUnreadCount() {
    if (_connectionId == null || _connectionId!.isEmpty) return;

    final getUnreadCountData = GetUnreadCountDTO(); // Para contagem geral

    final chatMessageDto = ChatMessageDTO<GetUnreadCountDTO>(
      action: ChatActionEnumerator.getUnreadCount,
      connectionId: _connectionId,
      data: getUnreadCountData,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  void handleBack() {
    Modular.to.pop();
  }

  void openMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext bottomSheetContext) {
        return FriendOptionsBottomSheet(
          doInviteToPlay: _doInviteToPlay,
          doEraseChatHistory: _doEraseChatHistory,
          doUnmatchPlayer: _doUnmatchPlayer,
          doReportPlayer: () =>
              _doReportPlayer(context), // Passa o context da tela
          doBlockPlayer: () =>
              _doBlockPlayer(context), // Passa o context da tela
        );
      },
    );
  }

  // Funções de ação para o BottomSheet
  void _doInviteToPlay() {
    // TODO: Implementar lógica para convidar para jogar
    print("Ação: Convidar para jogar");
  }

  void _doEraseChatHistory() {
    // TODO: Implementar lógica para apagar histórico do chat
    print("Ação: Apagar histórico do chat");
  }

  void _doUnmatchPlayer() {
    // TODO: Implementar lógica para descombinar jogador
    print("Ação: Descombinar jogador");
  }

  void _doReportPlayer(BuildContext pageContext) async {
    final res = await _controller.reportUser(
        int.parse(widget.friend.id), ReportReason.abusiveContent);
    if (res == 1) {
      showDialog(
        context: pageContext,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return ReportPlayerDialog(
            onBlockPlayer: () {
              _doBlockPlayer(pageContext);
            },
            onReturn: () {},
          );
        },
      );
      return;
    }
    if (res == 0) {
      ShowMessage(
        noAvatar: true,
        message: 'This player has already been reported by you.',
        noButton: true,
        duration: Duration(seconds: 3),
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem reporting the player, please try again later',
      noButton: true,
      duration: Duration(seconds: 3),
    );
  }

  void _doBlockPlayer(BuildContext pageContext) async {
    final res = await _controller.blockUser(int.parse(widget.friend.id));
    if (res) {
      showDialog(
        context: pageContext, // Usa o context da página para o showDialog
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return BlockPlayerDialog(
            onDone: () {},
          );
        },
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem blocking the player, please try again later',
    );
  }

  PreferredSize _buildAppHeader() {
    final friendName = widget.friend.name;
    final friendStatus = "last online 2m ago"; // Mock
    final friendAvatarUrl = widget.friend.avatarUrl;
    final height = showInfoBanner ? 9.4.h : 11.h;
    return PreferredSize(
      preferredSize: Size.fromHeight(height),
      child: SafeArea(
        child: AppHeader(
          logoSectionLeftWidget: IconButton(
              onPressed: handleBack,
              icon: const Icon(
                QuyckyIcons.arrow_left_circle,
                color: Colors.white,
                size: 23,
              )),
          logoSectionRightWidget: IconButton(
              onPressed: openMenu,
              icon: const Icon(Icons.more_vert, color: Colors.white)),
          centerChild: Container(
            color: Colors.transparent,
            width: 67.w,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Avatar(
                  imagePath: friendAvatarUrl,
                  size: 6.87.h,
                  addPhotoButton: false,
                  borderColor: Colors.white,
                  borderWidth: 1.5,
                ),
                SizedBox(width: 4.1.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friendName,
                      style: const TextStyle(
                          fontFamily: 'Roboto',
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          letterSpacing: 0.1),
                    ),
                    Text(
                      friendStatus,
                      style: const TextStyle(
                        fontFamily: 'Roboto',
                        color: Colors.white,
                        letterSpacing: -0.2,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _verifyAndBuildChatBlockedOverlay() {
    if (!_isUserSocialLogged) {
      return Container();
    }

    return ClipRRect(
        clipBehavior: Clip.antiAlias,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
          child: Container(
              width: 100.w,
              height: 100.h,
              decoration: BoxDecoration(color: Colors.white.withAlpha(85)),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 10.h,
                    ),
                    CustomImage(
                      Assets.svg.padlockWithCircle,
                      width: 10.h,
                      height: 10.h,
                    ),
                    Padding(
                        padding: EdgeInsets.only(top: 3.55.h, bottom: 1.78.h),
                        child: Text(
                          'CHAT LOCKED',
                          style: TextStyle(
                            fontFamily: 'Roboto',
                            fontSize: 16,
                            letterSpacing: 0.1,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )),
                    Text(
                      'You must sign in with a social account\nto unlock the chat feature',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 15,
                        letterSpacing: 0.25,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(
                      height: 4.6.h,
                    ),
                    SizedBox(
                      width: 83.34.w,
                      child: SocialLoginButtons(
                        callback: () {
                          setTimeout(
                              callback: () => setState(() {}),
                              duration: Duration(seconds: 1));
                        },
                      ),
                    )
                  ])),
        ));
  }

  @override
  Widget build(BuildContext context) {
    final chatListItems = _buildChatListItems();

    return GradientContainer(
      useDefault: true,
      coldOpacity: 0,
      normalOpacity: 0,
      hotOpacity: 0,
      child: Scaffold(
        backgroundColor: Colors.transparent, // Cor de fundo da tela
        appBar: _buildAppHeader(),
        body: Stack(
          children: [
            Column(
              children: [
                if (showInfoBanner)
                  _buildInfoBanner(), // Adiciona o banner aqui
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16.0),
                    itemCount: chatListItems.length,
                    itemBuilder: (context, index) {
                      return chatListItems[index];
                    },
                  ),
                ),
                _buildMessageInputField(),
              ],
            ),
            _verifyAndBuildChatBlockedOverlay(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildChatListItems() {
    final List<Widget> items = [];
    bool todaySeparatorShown = false;
    final now = DateTime.now();

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      final messageDate = message.createdAt;

      // Verifica se a mensagem é de hoje
      final bool isMessageToday = messageDate.year == now.year &&
          messageDate.month == now.month &&
          messageDate.day == now.day;

      if (isMessageToday && !todaySeparatorShown) {
        items.add(_buildDateSeparator("Today"));
        todaySeparatorShown = true;
      }
      // TODO: Adicionar lógica para outros separadores de data (ex: "Yesterday", "dd/MM/yyyy")
      // else if (!isMessageToday) {
      //   // Lógica para mostrar outras datas se necessário, comparando com a data da mensagem anterior
      //   if (i == 0 ||
      //       _messages[i-1].timestamp.year != messageDate.year ||
      //       _messages[i-1].timestamp.month != messageDate.month ||
      //       _messages[i-1].timestamp.day != messageDate.day) {
      //         // items.add(_buildDateSeparator(DateFormat('dd MMM').format(messageDate)));
      //       }
      // }
      items.add(_buildMessageBubble(message));
    }
    return items;
  }

  Widget _buildInfoBanner() {
    return Container(
      color: CustomColors.primary, // Cor de fundo do banner
      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.5.h),
      child: Row(
        children: [
          IconButton(
            icon: Icon(QuyckyIcons.close_circle,
                color: Colors.white, size: 2.97.h),
            onPressed: () {
              if (mounted) {
                // Adicionado mounted check
                setState(() {
                  _showInfoBanner = false;
                });
              }
            },
          ),
          const SizedBox(width: 8.0),
          Expanded(
              child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                letterSpacing: 0.2,
                height: 1.2,
                fontFamily: 'Roboto',
              ),
              children: <TextSpan>[
                TextSpan(text: 'Messages will vanish '),
                TextSpan(
                  text: '24 hours\n',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
                TextSpan(text: 'after they are sent. - '),
                TextSpan(
                  text: 'Don’t miss out!',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  void _handleChangeMessageTextFieldBorderRadius() {
    if (mounted) {
      // Adicionado mounted check
      setState(() {
        // _messageTextFieldBorderRadius = // Esta variável é final, não pode ser reatribuída.
        //     _messageController.text.isEmpty ? 122 : 20;
        // A lógica de animação do raio da borda já está no AnimatedContainer,
        // esta função pode ser usada para forçar um rebuild se necessário,
        // mas a mudança do raio em si é baseada no _messageController.text.isEmpty
        // dentro do _buildMessageInputField.
        // Se a intenção era mudar uma variável de estado para o raio, ela não deve ser final.
        // Por ora, vou manter a lógica original do AnimatedContainer que lê _messageController.text.isEmpty.
        // Se _messageTextFieldBorderRadius precisa ser uma variável de estado, ela não deve ser final.
        // Como é final, esta função não tem efeito prático em mudar o raio.
        // Apenas causa um rebuild.
      });
    }
  }

  Widget _buildDateSeparator(String date) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        decoration: BoxDecoration(
          color: CustomColors.sunsetOrange,
          borderRadius: BorderRadius.circular(122.0),
        ),
        child: Text(
          date,
          style: const TextStyle(color: Colors.white, fontSize: 12.0),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessageEntity message) {
    final bool isMe =
        message.senderIdentifier == _userStore.state.user.identifier;
    final align = isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start;
    final actualBubbleColor =
        isMe ? CustomColors.primary : CustomColors.sunsetOrange;

    final formattedTime = DateFormat('HH:mm').format(message.createdAt);

    return Column(
      crossAxisAlignment: align,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
          decoration: BoxDecoration(
            color: actualBubbleColor,
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(20.0),
              topRight: const Radius.circular(20.0),
              bottomLeft: isMe
                  ? const Radius.circular(20.0)
                  : const Radius.circular(0.0),
              bottomRight: isMe
                  ? const Radius.circular(0.0)
                  : const Radius.circular(20.0),
            ),
          ),
          child: Text(
            message.content,
            style: const TextStyle(color: Colors.white, fontSize: 15),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 2.0, bottom: 8.0),
          child: Text(
            formattedTime,
            style:
                TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 10.0),
          ),
        )
      ],
    );
  }

  Widget _buildMessageInputField() {
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.only(left: 6.41.w, right: 6.41.w, bottom: 3.h),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: AnimatedContainer(
                duration: Duration(milliseconds: 300), // Animação mais rápida
                decoration: BoxDecoration(
                  color: CustomColors.orangeSoda,
                  borderRadius: BorderRadius.circular(
                      _messageController.text.isEmpty
                          ? 122
                          : 20 // Lógica do raio aqui
                      ),
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: 21.32.h,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 1.h, horizontal: 1.w), // Ajuste no padding
                    child: TextField(
                      controller: _messageController,
                      style: const TextStyle(color: Colors.white),
                      minLines: 1,
                      maxLines:
                          5, // Limita o número de linhas para evitar expansão excessiva
                      textInputAction: TextInputAction.send,
                      keyboardType: TextInputType.multiline,
                      decoration: InputDecoration(
                        hintText: 'Type something...',
                        hintStyle: TextStyle(
                            color: Colors.white
                                .withOpacity(0.7), // Hint mais suave
                            fontFamily: 'Roboto',
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            letterSpacing: 0.2),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16.0, // Padding interno
                            vertical: 10.0),
                        suffixIcon: IconButton(
                          // Movido para fora do Padding
                          icon: Icon(
                            QuyckyIcons.send,
                            color: Colors.white,
                            size: 3.2.h,
                          ),
                          onPressed: _sendMessage,
                        ),
                      ),
                      onChanged: (value) {
                        if (mounted) {
                          // Adicionado mounted check
                          setState(() {
                            // Apenas para forçar o rebuild do AnimatedContainer
                          });
                        }
                      },
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
// FECHAMENTO DA CLASSE _FriendChatState ABAIXO
// A chave extra será removida abaixo
}
